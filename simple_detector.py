#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版植物大战僵尸屏幕检测器
使用OpenCV显示检测结果
"""

import cv2
import numpy as np
import time
import mss
from ultralytics import YOLO
import config

class SimpleScreenDetector:
    def __init__(self):
        """初始化检测器"""
        self.model = None
        self.sct = mss.mss()
        self.window_created = False
        self.window_pos = None
        self.window_size = None
        self.load_model()
        
    def load_model(self):
        """加载YOLOv8模型"""
        try:
            print(f"正在加载模型: {config.MODEL_PATH}")
            self.model = YOLO(config.MODEL_PATH)
            print("模型加载成功!")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def capture_screen(self):
        """截取屏幕，避免截取检测窗口"""
        monitor = self.sct.monitors[1]  # 主显示器

        # 如果检测窗口已创建，隐藏窗口后截图
        if self.window_created:
            # 隐藏窗口
            cv2.setWindowProperty(config.WINDOW_NAME, cv2.WND_PROP_VISIBLE, 0)
            time.sleep(0.01)  # 短暂等待窗口隐藏

        # 截取屏幕
        screenshot = self.sct.grab(monitor)
        img = np.array(screenshot)
        img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

        # 恢复窗口显示
        if self.window_created:
            cv2.setWindowProperty(config.WINDOW_NAME, cv2.WND_PROP_VISIBLE, 1)

        return img
    
    def detect_and_draw(self, image):
        """检测并绘制结果"""
        # 检测对象
        results = self.model(
            image,
            conf=config.CONFIDENCE_THRESHOLD,
            iou=config.IOU_THRESHOLD,
            verbose=False
        )
        
        if not results or results[0].boxes is None:
            return image
        
        # 获取检测结果
        boxes = results[0].boxes.xyxy.cpu().numpy()
        confidences = results[0].boxes.conf.cpu().numpy()
        classes = results[0].boxes.cls.cpu().numpy().astype(int)
        
        # 绘制边框和标签
        for box, conf, cls in zip(boxes, confidences, classes):
            x1, y1, x2, y2 = map(int, box)
            
            # 获取类别名称
            class_name = config.CLASS_NAMES.get(cls, f"Class_{cls}")
            
            # 绘制红色边框
            cv2.rectangle(image, (x1, y1), (x2, y2), config.BBOX_COLOR, config.BBOX_THICKNESS)
            
            # 绘制标签背景
            label = f"{class_name}: {conf:.2f}"
            (text_width, text_height), _ = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, config.FONT_SCALE, config.TEXT_THICKNESS
            )
            
            cv2.rectangle(
                image, 
                (x1, y1 - text_height - 10), 
                (x1 + text_width, y1), 
                config.BBOX_COLOR, 
                -1
            )
            
            # 绘制标签文字
            cv2.putText(
                image, 
                label, 
                (x1, y1 - 5), 
                cv2.FONT_HERSHEY_SIMPLEX, 
                config.FONT_SCALE, 
                config.TEXT_COLOR, 
                config.TEXT_THICKNESS
            )
        
        return image
    
    def run(self):
        """运行检测器"""
        print("启动屏幕检测器...")
        print("按 'q' 退出，按 'p' 暂停/继续")

        paused = False

        # 创建窗口并设置属性
        cv2.namedWindow(config.WINDOW_NAME, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(config.WINDOW_NAME, 800, 600)  # 设置初始窗口大小

        # 将窗口移动到屏幕右侧，避免截取到自己
        screen_width = self.sct.monitors[1]['width']
        cv2.moveWindow(config.WINDOW_NAME, screen_width - 850, 50)

        self.window_created = True

        while True:
            if not paused:
                # 截取屏幕
                screen = self.capture_screen()

                # 检测并绘制
                result_image = self.detect_and_draw(screen)

                # 缩放显示到合适大小
                height, width = result_image.shape[:2]
                display_width = 800
                display_height = int(height * (display_width / width))
                result_image = cv2.resize(result_image, (display_width, display_height))

                # 显示结果
                cv2.imshow(config.WINDOW_NAME, result_image)

            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('p'):
                paused = not paused
                status = "暂停" if paused else "运行"
                print(f"检测状态: {status}")

            # 控制帧率
            time.sleep(1.0 / config.FPS)
        
        cv2.destroyAllWindows()

def main():
    """主函数"""
    try:
        detector = SimpleScreenDetector()
        detector.run()
    except Exception as e:
        print(f"程序运行失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
