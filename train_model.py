#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv8模型训练脚本
用于训练植物大战僵尸的检测模型
"""

from ultralytics import YOLO
import yaml
import os

def create_dataset_config():
    """创建数据集配置文件"""
    dataset_config = {
        'path': './dataset',  # 数据集根目录
        'train': 'images/train',  # 训练图片目录
        'val': 'images/val',  # 验证图片目录
        'test': 'images/test',  # 测试图片目录（可选）
        
        # 类别数量
        'nc': 2,
        
        # 类别名称
        'names': {
            0: '僵尸',
            1: '植物'
        }
    }
    
    # 保存配置文件
    with open('dataset.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
    
    print("数据集配置文件已创建: dataset.yaml")
    return 'dataset.yaml'

def train_model():
    """训练YOLOv8模型"""
    # 创建数据集配置
    config_path = create_dataset_config()
    
    # 加载预训练模型
    model = YOLO('yolov8n.pt')  # 使用nano版本，速度快
    # model = YOLO('yolov8s.pt')  # 使用small版本，精度更高
    # model = YOLO('yolov8m.pt')  # 使用medium版本，平衡速度和精度
    
    # 训练参数
    results = model.train(
        data=config_path,           # 数据集配置文件
        epochs=100,                 # 训练轮数
        imgsz=640,                  # 图片尺寸
        batch=16,                   # 批次大小
        device='0',                 # GPU设备（'cpu' 使用CPU）
        workers=4,                  # 数据加载线程数
        project='runs/train',       # 项目目录
        name='pvz_detector',        # 实验名称
        save=True,                  # 保存检查点
        save_period=10,             # 每10轮保存一次
        cache=True,                 # 缓存数据集
        
        # 数据增强参数
        hsv_h=0.015,               # 色调增强
        hsv_s=0.7,                 # 饱和度增强
        hsv_v=0.4,                 # 明度增强
        degrees=0.0,               # 旋转角度
        translate=0.1,             # 平移
        scale=0.5,                 # 缩放
        shear=0.0,                 # 剪切
        perspective=0.0,           # 透视变换
        flipud=0.0,                # 上下翻转
        fliplr=0.5,                # 左右翻转
        mosaic=1.0,                # 马赛克增强
        mixup=0.0,                 # 混合增强
        
        # 优化器参数
        optimizer='SGD',           # 优化器类型
        lr0=0.01,                  # 初始学习率
        lrf=0.01,                  # 最终学习率
        momentum=0.937,            # 动量
        weight_decay=0.0005,       # 权重衰减
        warmup_epochs=3.0,         # 预热轮数
        warmup_momentum=0.8,       # 预热动量
        warmup_bias_lr=0.1,        # 预热偏置学习率
        
        # 其他参数
        box=7.5,                   # 边框损失权重
        cls=0.5,                   # 分类损失权重
        dfl=1.5,                   # DFL损失权重
        pose=12.0,                 # 姿态损失权重
        kobj=2.0,                  # 关键点对象损失权重
        label_smoothing=0.0,       # 标签平滑
        nbs=64,                    # 标准批次大小
        overlap_mask=True,         # 重叠掩码
        mask_ratio=4,              # 掩码比例
        dropout=0.0,               # Dropout率
        val=True,                  # 验证
        plots=True,                # 保存训练图表
        verbose=True,              # 详细输出
    )
    
    print("训练完成!")
    print(f"最佳模型保存在: {results.save_dir}/weights/best.pt")
    
    return results

def validate_model(model_path='runs/train/pvz_detector/weights/best.pt'):
    """验证训练好的模型"""
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        return
    
    # 加载训练好的模型
    model = YOLO(model_path)
    
    # 在验证集上评估
    results = model.val(data='dataset.yaml')
    
    print("验证结果:")
    print(f"mAP50: {results.box.map50:.4f}")
    print(f"mAP50-95: {results.box.map:.4f}")
    
    return results

def export_model(model_path='runs/train/pvz_detector/weights/best.pt'):
    """导出模型为不同格式"""
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        return
    
    model = YOLO(model_path)
    
    # 导出为ONNX格式（推荐用于部署）
    model.export(format='onnx', dynamic=True, simplify=True)
    
    # 导出为TensorRT格式（NVIDIA GPU加速）
    # model.export(format='engine', device=0)
    
    print("模型导出完成!")

def print_dataset_structure():
    """打印数据集目录结构说明"""
    structure = """
数据集目录结构应该如下：

dataset/
├── images/
│   ├── train/          # 训练图片
│   │   ├── img1.jpg
│   │   ├── img2.jpg
│   │   └── ...
│   ├── val/            # 验证图片
│   │   ├── img1.jpg
│   │   ├── img2.jpg
│   │   └── ...
│   └── test/           # 测试图片（可选）
│       ├── img1.jpg
│       └── ...
└── labels/
    ├── train/          # 训练标签
    │   ├── img1.txt
    │   ├── img2.txt
    │   └── ...
    ├── val/            # 验证标签
    │   ├── img1.txt
    │   ├── img2.txt
    │   └── ...
    └── test/           # 测试标签（可选）
        ├── img1.txt
        └── ...

标签文件格式（YOLO格式）：
每行一个对象：class_id center_x center_y width height
例如：0 0.5 0.5 0.3 0.4
- class_id: 类别ID（0=僵尸, 1=植物）
- center_x, center_y: 中心点坐标（相对于图片尺寸，0-1）
- width, height: 宽度和高度（相对于图片尺寸，0-1）
"""
    print(structure)

def main():
    """主函数"""
    print("植物大战僵尸 YOLOv8 模型训练工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看数据集目录结构说明")
        print("2. 创建数据集配置文件")
        print("3. 开始训练模型")
        print("4. 验证模型")
        print("5. 导出模型")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == '1':
            print_dataset_structure()
        elif choice == '2':
            create_dataset_config()
        elif choice == '3':
            if not os.path.exists('dataset'):
                print("错误: 数据集目录不存在，请先准备数据集")
                continue
            train_model()
        elif choice == '4':
            model_path = input("请输入模型路径 (默认: runs/train/pvz_detector/weights/best.pt): ").strip()
            if not model_path:
                model_path = 'runs/train/pvz_detector/weights/best.pt'
            validate_model(model_path)
        elif choice == '5':
            model_path = input("请输入模型路径 (默认: runs/train/pvz_detector/weights/best.pt): ").strip()
            if not model_path:
                model_path = 'runs/train/pvz_detector/weights/best.pt'
            export_model(model_path)
        elif choice == '6':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
