#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无窗口覆盖检测器
直接在屏幕上绘制检测框，无需额外窗口
"""

import cv2
import numpy as np
import time
import mss
import threading
import tkinter as tk
from ultralytics import YOLO
import win32gui
import win32con
import win32api
from PIL import Image, ImageDraw, ImageFont
import config

class OverlayDetector:
    def __init__(self):
        """初始化覆盖检测器"""
        self.model = None
        self.sct = mss.mss()
        self.running = False
        self.paused = False
        self.detections = []
        self.detection_lock = threading.Lock()
        
        # 加载模型
        self.load_model()
        
        # 创建透明覆盖窗口
        self.create_overlay()
        
    def load_model(self):
        """加载YOLOv8模型"""
        try:
            print(f"正在加载模型: {config.MODEL_PATH}")
            self.model = YOLO(config.MODEL_PATH)
            print("模型加载成功!")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def create_overlay(self):
        """创建透明覆盖窗口"""
        self.root = tk.Tk()
        self.root.title("PVZ检测覆盖")
        
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 设置窗口为全屏透明
        self.root.geometry(f"{screen_width}x{screen_height}+0+0")
        self.root.attributes('-topmost', True)  # 置顶
        self.root.attributes('-transparentcolor', 'black')  # 黑色透明
        self.root.overrideredirect(True)  # 无边框
        
        # 创建画布
        self.canvas = tk.Canvas(
            self.root,
            width=screen_width,
            height=screen_height,
            bg='black',
            highlightthickness=0
        )
        self.canvas.pack()
        
        # 绑定键盘事件
        self.root.bind('<KeyPress>', self.on_key_press)
        self.root.focus_set()
        
        # 使窗口点击穿透
        self.make_click_through()
    
    def make_click_through(self):
        """使窗口点击穿透"""
        try:
            # 获取窗口句柄
            hwnd = self.root.winfo_id()
            
            # 设置窗口样式为点击穿透
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            style |= win32con.WS_EX_LAYERED | win32con.WS_EX_TRANSPARENT
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, style)
            
            print("窗口已设置为点击穿透")
        except Exception as e:
            print(f"设置点击穿透失败: {e}")
    
    def capture_screen(self):
        """截取屏幕，排除覆盖窗口"""
        # 临时隐藏覆盖窗口
        self.root.withdraw()
        time.sleep(0.01)
        
        # 截取屏幕
        monitor = self.sct.monitors[1]
        screenshot = self.sct.grab(monitor)
        img = np.array(screenshot)
        img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
        
        # 恢复覆盖窗口
        self.root.deiconify()
        
        return img
    
    def detect_objects(self, image):
        """检测对象"""
        try:
            results = self.model(
                image,
                conf=config.CONFIDENCE_THRESHOLD,
                iou=config.IOU_THRESHOLD,
                verbose=False
            )
            return results[0] if results else None
        except Exception as e:
            print(f"检测错误: {e}")
            return None
    
    def draw_detections(self):
        """在覆盖窗口上绘制检测结果"""
        # 清除之前的绘制
        self.canvas.delete("all")
        
        with self.detection_lock:
            if not self.detections:
                return
            
            for detection in self.detections:
                x1, y1, x2, y2, conf, cls = detection
                
                # 获取类别名称
                class_name = config.CLASS_NAMES.get(cls, f"Class_{cls}")
                
                # 设置颜色
                if cls == 0:  # 僵尸
                    color = 'red'
                    text_color = 'white'
                else:  # 植物
                    color = 'lime'
                    text_color = 'black'
                
                # 绘制边框
                self.canvas.create_rectangle(
                    x1, y1, x2, y2,
                    outline=color,
                    width=config.BBOX_THICKNESS
                )
                
                # 绘制标签背景
                label = f"{class_name}: {conf:.2f}"
                self.canvas.create_rectangle(
                    x1, y1 - 25, x1 + len(label) * 8, y1,
                    fill=color,
                    outline=color
                )
                
                # 绘制标签文字
                self.canvas.create_text(
                    x1 + 2, y1 - 12,
                    text=label,
                    fill=text_color,
                    font=('Arial', 10, 'bold'),
                    anchor='w'
                )
    
    def detection_loop(self):
        """检测循环"""
        while self.running:
            if not self.paused:
                try:
                    # 截取屏幕
                    screen = self.capture_screen()
                    
                    # 检测对象
                    results = self.detect_objects(screen)
                    
                    # 更新检测结果
                    new_detections = []
                    if results and results.boxes is not None:
                        boxes = results.boxes.xyxy.cpu().numpy()
                        confidences = results.boxes.conf.cpu().numpy()
                        classes = results.boxes.cls.cpu().numpy().astype(int)
                        
                        for box, conf, cls in zip(boxes, confidences, classes):
                            x1, y1, x2, y2 = map(int, box)
                            new_detections.append((x1, y1, x2, y2, conf, cls))
                    
                    with self.detection_lock:
                        self.detections = new_detections
                    
                    # 绘制结果
                    self.root.after(0, self.draw_detections)
                    
                except Exception as e:
                    print(f"检测循环错误: {e}")
            
            # 控制帧率
            time.sleep(1.0 / config.FPS)
    
    def on_key_press(self, event):
        """键盘事件处理"""
        key = event.char.lower()
        
        if key == config.EXIT_KEY:
            self.stop()
        elif key == config.PAUSE_KEY:
            self.paused = not self.paused
            status = "暂停" if self.paused else "运行"
            print(f"检测状态: {status}")
    
    def start(self):
        """启动检测器"""
        print("启动无窗口覆盖检测器...")
        print(f"按 '{config.EXIT_KEY}' 退出，按 '{config.PAUSE_KEY}' 暂停/继续")
        print("检测框将直接显示在屏幕上，不影响游戏操作")
        
        self.running = True
        
        # 在单独线程中运行检测循环
        detection_thread = threading.Thread(target=self.detection_loop)
        detection_thread.daemon = True
        detection_thread.start()
        
        # 启动GUI主循环
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """停止检测器"""
        print("正在停止检测器...")
        self.running = False
        if self.root:
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    try:
        detector = OverlayDetector()
        detector.start()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
