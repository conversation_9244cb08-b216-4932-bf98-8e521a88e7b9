#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
植物大战僵尸 YOLOv8 屏幕检测器
实时检测屏幕上的僵尸和植物，并绘制红色边框
"""

import cv2
import numpy as np
import time
import mss
import threading
from ultralytics import YOLO
import tkinter as tk
from PIL import Image, ImageTk
import config

class ScreenDetector:
    def __init__(self):
        """初始化屏幕检测器"""
        self.model = None
        self.running = False
        self.paused = False
        self.sct = mss.mss()
        self.overlay_window = None
        self.canvas = None
        self.root = None
        
        # 加载模型
        self.load_model()
        
        # 创建覆盖窗口
        self.create_overlay_window()
        
    def load_model(self):
        """加载YOLOv8模型"""
        try:
            print(f"正在加载模型: {config.MODEL_PATH}")
            self.model = YOLO(config.MODEL_PATH)
            print("模型加载成功!")
            
            # 验证模型类别
            if hasattr(self.model, 'names'):
                print(f"模型类别: {self.model.names}")
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            print("请确保模型文件存在且路径正确")
            raise
    
    def create_overlay_window(self):
        """创建透明覆盖窗口"""
        self.root = tk.Tk()
        self.root.title(config.WINDOW_NAME)
        
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 设置窗口属性
        self.root.geometry(f"{screen_width}x{screen_height}+0+0")
        self.root.attributes('-topmost', True)  # 置顶
        self.root.attributes('-alpha', config.WINDOW_ALPHA)  # 透明度
        self.root.overrideredirect(True)  # 无边框
        
        # 创建画布
        self.canvas = tk.Canvas(
            self.root, 
            width=screen_width, 
            height=screen_height,
            highlightthickness=0,
            bg='black'
        )
        self.canvas.pack()
        
        # 绑定键盘事件
        self.root.bind('<KeyPress>', self.on_key_press)
        self.root.focus_set()
        
    def capture_screen(self):
        """截取屏幕"""
        if config.SCREENSHOT_REGION:
            # 指定区域截图
            monitor = {
                "top": config.SCREENSHOT_REGION[1],
                "left": config.SCREENSHOT_REGION[0],
                "width": config.SCREENSHOT_REGION[2],
                "height": config.SCREENSHOT_REGION[3]
            }
        else:
            # 全屏截图
            monitor = self.sct.monitors[1]  # 主显示器
        
        screenshot = self.sct.grab(monitor)
        img = np.array(screenshot)
        img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
        return img
    
    def detect_objects(self, image):
        """使用YOLOv8检测对象"""
        try:
            results = self.model(
                image,
                conf=config.CONFIDENCE_THRESHOLD,
                iou=config.IOU_THRESHOLD,
                verbose=False
            )
            return results[0] if results else None
        except Exception as e:
            print(f"检测错误: {e}")
            return None
    
    def draw_detections(self, detections):
        """在覆盖窗口上绘制检测结果"""
        # 清除之前的绘制
        self.canvas.delete("all")
        
        if detections is None or detections.boxes is None:
            return
        
        boxes = detections.boxes.xyxy.cpu().numpy()
        confidences = detections.boxes.conf.cpu().numpy()
        classes = detections.boxes.cls.cpu().numpy().astype(int)
        
        for box, conf, cls in zip(boxes, confidences, classes):
            x1, y1, x2, y2 = map(int, box)
            
            # 获取类别名称
            class_name = config.CLASS_NAMES.get(cls, f"Class_{cls}")
            
            # 绘制边框
            self.canvas.create_rectangle(
                x1, y1, x2, y2,
                outline='red',
                width=config.BBOX_THICKNESS
            )
            
            # 绘制标签
            label = f"{class_name}: {conf:.2f}"
            self.canvas.create_text(
                x1, y1 - 10,
                text=label,
                fill='white',
                font=('Arial', 10, 'bold'),
                anchor='sw'
            )
    
    def detection_loop(self):
        """检测循环"""
        while self.running:
            if not self.paused:
                try:
                    # 截取屏幕
                    screen = self.capture_screen()
                    
                    # 检测对象
                    detections = self.detect_objects(screen)
                    
                    # 绘制结果
                    self.draw_detections(detections)
                    
                except Exception as e:
                    print(f"检测循环错误: {e}")
            
            # 控制帧率
            time.sleep(1.0 / config.FPS)
    
    def on_key_press(self, event):
        """键盘事件处理"""
        key = event.char.lower()
        
        if key == config.EXIT_KEY:
            self.stop()
        elif key == config.PAUSE_KEY:
            self.paused = not self.paused
            status = "暂停" if self.paused else "运行"
            print(f"检测状态: {status}")
    
    def start(self):
        """启动检测器"""
        print("启动屏幕检测器...")
        print(f"按 '{config.EXIT_KEY}' 退出，按 '{config.PAUSE_KEY}' 暂停/继续")
        
        self.running = True
        
        # 在单独线程中运行检测循环
        detection_thread = threading.Thread(target=self.detection_loop)
        detection_thread.daemon = True
        detection_thread.start()
        
        # 启动GUI主循环
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """停止检测器"""
        print("正在停止检测器...")
        self.running = False
        if self.root:
            self.root.quit()
            self.root.destroy()

def main():
    """主函数"""
    try:
        detector = ScreenDetector()
        detector.start()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
