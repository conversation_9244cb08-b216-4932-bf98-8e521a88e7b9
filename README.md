# 植物大战僵尸 YOLOv8 屏幕检测器

这是一个使用YOLOv8模型进行实时屏幕检测的程序，专门用于识别植物大战僵尸游戏中的僵尸和植物。

## 功能特点

- 🎯 实时屏幕检测：使用YOLOv8模型实时检测屏幕上的对象
- 🧟 僵尸识别：自动识别屏幕上的僵尸（标签0）
- 🌱 植物识别：自动识别屏幕上的植物（标签1）
- 🔴 红框标记：在识别到的对象周围绘制红色边框
- ⚡ 高性能：优化的检测算法，流畅的实时体验
- 🎮 游戏友好：透明覆盖窗口，不影响游戏操作

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 准备模型文件

确保您有训练好的YOLOv8模型文件（通常命名为 `best.pt`），并将其放在项目根目录下。

### 2. 配置设置

编辑 `config.py` 文件来调整检测参数：

- `MODEL_PATH`: 模型文件路径
- `CONFIDENCE_THRESHOLD`: 置信度阈值（0-1）
- `SCREENSHOT_REGION`: 截图区域（None为全屏）
- `FPS`: 检测帧率

### 3. 运行程序

```bash
python screen_detector.py
```

### 4. 运行模式选择

程序提供三种运行模式：

1. **透明覆盖模式**: 创建透明窗口覆盖在屏幕上，不影响游戏操作
2. **区域检测模式**: 只检测屏幕指定区域，完全避免窗口叠加问题
3. **简单窗口模式**: 在独立窗口显示结果，已优化减少叠加问题

### 5. 控制操作

- **q**: 退出程序
- **p**: 暂停/继续检测
- **r**: 重新设置检测区域（仅区域模式）

## 类别说明

- **标签0**: 僵尸 🧟
- **标签1**: 植物 🌱

## 配置选项

### 检测参数
- `CONFIDENCE_THRESHOLD`: 检测置信度阈值，越高越严格
- `IOU_THRESHOLD`: NMS重叠阈值，用于去除重复检测

### 显示参数
- `BBOX_COLOR`: 边框颜色（BGR格式）
- `BBOX_THICKNESS`: 边框粗细
- `WINDOW_ALPHA`: 窗口透明度（0-1）

### 性能参数
- `FPS`: 检测帧率，影响CPU使用率和检测延迟

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确保模型文件完整且未损坏

2. **检测效果不佳**
   - 调整置信度阈值
   - 检查模型是否适合当前场景

3. **性能问题**
   - 降低FPS设置
   - 使用GPU加速（需要CUDA支持）

4. **窗口显示问题**
   - 调整窗口透明度
   - 检查是否有其他程序干扰

5. **窗口叠加问题**
   - 简单窗口模式可能出现无限叠加
   - 推荐使用区域检测模式避免此问题
   - 区域检测模式支持左半屏、上半屏或自定义区域

## 系统要求

- Python 3.8+
- Windows/Linux/macOS
- 至少4GB RAM
- 推荐使用GPU加速

## 注意事项

- 程序会创建透明覆盖窗口，可能会影响某些全屏应用
- 首次运行时可能需要下载PyTorch模型
- 确保有足够的系统权限进行屏幕截图

## 许可证

本项目仅供学习和研究使用。
