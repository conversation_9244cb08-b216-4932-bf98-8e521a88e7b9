#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版检测器 - 完全避免窗口叠加问题
使用双缓冲和窗口排除技术
"""

import cv2
import numpy as np
import time
import mss
from ultralytics import YOLO
import win32gui
import win32con
import config

class FixedDetector:
    def __init__(self):
        """初始化检测器"""
        self.model = None
        self.sct = mss.mss()
        self.window_hwnd = None
        self.window_name = "植物大战僵尸检测 - 修复版"
        self.load_model()
        
    def load_model(self):
        """加载YOLOv8模型"""
        try:
            print(f"正在加载模型: {config.MODEL_PATH}")
            self.model = YOLO(config.MODEL_PATH)
            print("模型加载成功!")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def get_window_rect(self, hwnd):
        """获取窗口矩形区域"""
        try:
            rect = win32gui.GetWindowRect(hwnd)
            return rect
        except:
            return None
    
    def capture_screen_exclude_window(self):
        """截取屏幕，排除检测窗口区域"""
        # 获取主显示器
        monitor = self.sct.monitors[1]
        
        # 如果检测窗口存在，获取其位置
        window_rect = None
        if self.window_hwnd:
            window_rect = self.get_window_rect(self.window_hwnd)
        
        # 截取全屏
        screenshot = self.sct.grab(monitor)
        img = np.array(screenshot)
        img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
        
        # 如果检测窗口存在，将其区域填充为黑色
        if window_rect:
            x1, y1, x2, y2 = window_rect
            # 确保坐标在屏幕范围内
            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(monitor['width'], x2)
            y2 = min(monitor['height'], y2)
            
            if x2 > x1 and y2 > y1:
                img[y1:y2, x1:x2] = 0  # 填充为黑色
        
        return img
    
    def detect_and_draw(self, image):
        """检测并绘制结果"""
        # 检测对象
        results = self.model(
            image,
            conf=config.CONFIDENCE_THRESHOLD,
            iou=config.IOU_THRESHOLD,
            verbose=False
        )
        
        if not results or results[0].boxes is None:
            return image
        
        # 获取检测结果
        boxes = results[0].boxes.xyxy.cpu().numpy()
        confidences = results[0].boxes.conf.cpu().numpy()
        classes = results[0].boxes.cls.cpu().numpy().astype(int)
        
        detection_count = {'僵尸': 0, '植物': 0}
        
        # 绘制边框和标签
        for box, conf, cls in zip(boxes, confidences, classes):
            x1, y1, x2, y2 = map(int, box)
            
            # 获取类别名称
            class_name = config.CLASS_NAMES.get(cls, f"Class_{cls}")
            detection_count[class_name] += 1
            
            # 根据类别设置不同颜色
            if cls == 0:  # 僵尸
                bbox_color = (0, 0, 255)  # 红色
                text_bg_color = (0, 0, 255)
            else:  # 植物
                bbox_color = (0, 255, 0)  # 绿色
                text_bg_color = (0, 255, 0)
            
            # 绘制边框
            cv2.rectangle(image, (x1, y1), (x2, y2), bbox_color, config.BBOX_THICKNESS)
            
            # 绘制标签背景
            label = f"{class_name}: {conf:.2f}"
            (text_width, text_height), _ = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, config.FONT_SCALE, config.TEXT_THICKNESS
            )
            
            cv2.rectangle(
                image, 
                (x1, y1 - text_height - 10), 
                (x1 + text_width, y1), 
                text_bg_color, 
                -1
            )
            
            # 绘制标签文字
            cv2.putText(
                image, 
                label, 
                (x1, y1 - 5), 
                cv2.FONT_HERSHEY_SIMPLEX, 
                config.FONT_SCALE, 
                config.TEXT_COLOR, 
                config.TEXT_THICKNESS
            )
        
        # 在图像顶部显示统计信息
        stats_text = f"僵尸: {detection_count['僵尸']}  植物: {detection_count['植物']}"
        cv2.putText(
            image, 
            stats_text, 
            (10, 30), 
            cv2.FONT_HERSHEY_SIMPLEX, 
            0.8, 
            (255, 255, 255), 
            2
        )
        
        return image
    
    def find_window_hwnd(self):
        """查找检测窗口句柄"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if self.window_name in window_title:
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows[0] if windows else None
    
    def run(self):
        """运行检测器"""
        print("启动修复版屏幕检测器...")
        print("按 'q' 退出，按 'p' 暂停/继续")
        print("此版本完全避免窗口叠加问题")
        
        paused = False
        
        # 创建窗口
        cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(self.window_name, 800, 600)
        
        # 将窗口移动到屏幕右侧
        screen_width = self.sct.monitors[1]['width']
        cv2.moveWindow(self.window_name, screen_width - 850, 50)
        
        # 等待窗口创建完成
        time.sleep(0.5)
        
        # 查找窗口句柄
        self.window_hwnd = self.find_window_hwnd()
        if self.window_hwnd:
            print(f"找到检测窗口句柄: {self.window_hwnd}")
        else:
            print("警告: 未找到检测窗口句柄，可能仍有叠加问题")
        
        while True:
            if not paused:
                # 截取屏幕（排除检测窗口）
                screen = self.capture_screen_exclude_window()
                
                # 检测并绘制
                result_image = self.detect_and_draw(screen)
                
                # 缩放显示
                height, width = result_image.shape[:2]
                display_width = 800
                display_height = int(height * (display_width / width))
                result_image = cv2.resize(result_image, (display_width, display_height))
                
                # 显示结果
                cv2.imshow(self.window_name, result_image)
                
                # 更新窗口句柄（防止窗口重新创建）
                if not self.window_hwnd:
                    self.window_hwnd = self.find_window_hwnd()
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('p'):
                paused = not paused
                status = "暂停" if paused else "运行"
                print(f"检测状态: {status}")
            
            # 控制帧率
            time.sleep(1.0 / config.FPS)
        
        cv2.destroyAllWindows()

def main():
    """主函数"""
    try:
        detector = FixedDetector()
        detector.run()
    except Exception as e:
        print(f"程序运行失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
