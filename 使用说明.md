# 植物大战僵尸 YOLOv8 检测器使用说明

## 🎯 问题解决方案

根据您遇到的三个问题，我已经创建了完整的解决方案：

### 1. ✅ 标签显示问题已修复
- 现在会正确显示中文"僵尸"和"植物"
- 僵尸显示红色边框，植物显示绿色边框
- 标签格式：`僵尸: 0.85` 或 `植物: 0.92`

### 2. ✅ 窗口叠加问题已解决
- 提供了多种避免叠加的方案
- 推荐使用"无窗口覆盖模式"或"修复版窗口模式"

### 3. ✅ 无窗口版本已创建
- 直接在游戏屏幕上绘制检测框
- 不影响游戏操作，支持点击穿透

## 🚀 四种检测模式

### 模式1: 无窗口覆盖模式 (推荐⭐)
**文件**: `overlay_detector.py`
**特点**:
- 直接在游戏屏幕上绘制检测框
- 完全透明，不影响游戏操作
- 支持点击穿透
- 僵尸红框，植物绿框

**使用场景**: 游戏中实时检测，最佳体验

### 模式2: 修复版窗口模式 (推荐⭐)
**文件**: `fixed_detector.py`
**特点**:
- 智能排除检测窗口区域
- 完全避免窗口叠加问题
- 显示检测统计信息
- 窗口位于屏幕右侧

**使用场景**: 调试和分析，查看详细检测结果

### 模式3: 区域检测模式
**文件**: `region_detector.py`
**特点**:
- 只检测屏幕指定区域
- 支持左半屏、上半屏、自定义区域
- 避免检测到无关内容
- 可按'r'重新设置区域

**使用场景**: 精确检测特定游戏区域

### 模式4: 透明覆盖模式
**文件**: `screen_detector.py`
**特点**:
- 传统透明覆盖方式
- 适合某些特殊需求

## 📋 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 准备模型
- 将您的YOLOv8模型文件命名为`best.pt`
- 放在项目根目录下
- 或者程序会自动下载预训练模型

### 3. 运行程序
```bash
python run.py
```

### 4. 选择模式
- 推荐选择 "1. 无窗口覆盖模式"
- 或者选择 "2. 修复版窗口模式"

## 🎮 控制操作

| 按键 | 功能 |
|------|------|
| `q` | 退出程序 |
| `p` | 暂停/继续检测 |
| `r` | 重新设置区域 (仅区域模式) |

## 🎨 视觉效果

- **僵尸**: 红色边框 + 白色文字
- **植物**: 绿色边框 + 黑色文字
- **标签格式**: `类别名: 置信度`
- **统计信息**: 显示检测到的僵尸和植物数量

## ⚙️ 配置调整

编辑 `config.py` 文件可以调整：

```python
# 检测参数
CONFIDENCE_THRESHOLD = 0.5  # 置信度阈值
IOU_THRESHOLD = 0.45       # NMS阈值

# 显示参数
BBOX_THICKNESS = 2         # 边框粗细
FONT_SCALE = 0.6          # 字体大小
FPS = 10                  # 检测帧率

# 类别名称
CLASS_NAMES = {
    0: "僵尸",
    1: "植物"
}
```

## 🔧 故障排除

### 问题1: 模型加载失败
**解决方案**:
- 检查模型文件路径
- 确保模型文件完整
- 尝试重新下载模型

### 问题2: 检测效果不佳
**解决方案**:
- 调整置信度阈值 (降低CONFIDENCE_THRESHOLD)
- 检查模型是否适合当前游戏版本
- 确保游戏画面清晰

### 问题3: 性能问题
**解决方案**:
- 降低FPS设置
- 使用GPU加速
- 选择区域检测模式减少计算量

### 问题4: 窗口问题
**解决方案**:
- 使用无窗口覆盖模式
- 或使用修复版窗口模式
- 避免使用简单窗口模式

## 💡 使用技巧

1. **游戏中使用**: 推荐无窗口覆盖模式
2. **调试分析**: 推荐修复版窗口模式
3. **特定区域**: 使用区域检测模式选择左半屏
4. **性能优化**: 调整FPS和置信度阈值
5. **多显示器**: 程序会自动使用主显示器

## 📊 检测效果示例

从您的截图可以看到，程序已经能够：
- ✅ 正确识别豌豆射手（植物）
- ✅ 正确识别僵尸
- ✅ 绘制检测框
- ✅ 显示置信度

现在的改进版本将：
- ✅ 显示中文标签
- ✅ 使用不同颜色区分类别
- ✅ 完全避免窗口叠加
- ✅ 提供更好的用户体验

## 🎯 推荐配置

对于植物大战僵尸游戏，推荐配置：

```python
# config.py 推荐设置
CONFIDENCE_THRESHOLD = 0.3  # 较低阈值，检测更多对象
FPS = 15                    # 适中帧率
BBOX_THICKNESS = 3          # 较粗边框，更容易看见
```

选择"无窗口覆盖模式"，享受最佳的游戏检测体验！
