#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域检测版本 - 避免窗口叠加问题
通过指定截图区域来避免截取检测窗口
"""

import cv2
import numpy as np
import time
import mss
from ultralytics import YOLO
import tkinter as tk
from tkinter import messagebox
import config

class RegionScreenDetector:
    def __init__(self):
        """初始化检测器"""
        self.model = None
        self.sct = mss.mss()
        self.capture_region = None
        self.load_model()
        self.setup_capture_region()
        
    def load_model(self):
        """加载YOLOv8模型"""
        try:
            print(f"正在加载模型: {config.MODEL_PATH}")
            self.model = YOLO(config.MODEL_PATH)
            print("模型加载成功!")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def setup_capture_region(self):
        """设置截图区域"""
        # 获取屏幕尺寸
        monitor = self.sct.monitors[1]
        screen_width = monitor['width']
        screen_height = monitor['height']
        
        print(f"屏幕尺寸: {screen_width}x{screen_height}")
        print("\n请选择截图区域:")
        print("1. 左半屏 (游戏区域)")
        print("2. 上半屏")
        print("3. 自定义区域")
        print("4. 全屏 (可能有叠加问题)")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == '1':
            # 左半屏
            self.capture_region = {
                'left': 0,
                'top': 0,
                'width': screen_width // 2,
                'height': screen_height
            }
            print(f"设置为左半屏: {self.capture_region['width']}x{self.capture_region['height']}")
            
        elif choice == '2':
            # 上半屏
            self.capture_region = {
                'left': 0,
                'top': 0,
                'width': screen_width,
                'height': screen_height // 2
            }
            print(f"设置为上半屏: {self.capture_region['width']}x{self.capture_region['height']}")
            
        elif choice == '3':
            # 自定义区域
            try:
                print("请输入截图区域 (格式: x,y,width,height):")
                print("例如: 0,0,800,600")
                region_input = input("区域: ").strip()
                x, y, w, h = map(int, region_input.split(','))
                
                self.capture_region = {
                    'left': x,
                    'top': y,
                    'width': w,
                    'height': h
                }
                print(f"设置为自定义区域: {w}x{h} at ({x},{y})")
                
            except ValueError:
                print("输入格式错误，使用左半屏作为默认")
                self.capture_region = {
                    'left': 0,
                    'top': 0,
                    'width': screen_width // 2,
                    'height': screen_height
                }
        else:
            # 全屏
            self.capture_region = None
            print("设置为全屏")
    
    def capture_screen(self):
        """截取指定区域的屏幕"""
        if self.capture_region:
            # 截取指定区域
            screenshot = self.sct.grab(self.capture_region)
        else:
            # 全屏截图
            monitor = self.sct.monitors[1]
            screenshot = self.sct.grab(monitor)
        
        img = np.array(screenshot)
        img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
        return img
    
    def detect_and_draw(self, image):
        """检测并绘制结果"""
        # 检测对象
        results = self.model(
            image,
            conf=config.CONFIDENCE_THRESHOLD,
            iou=config.IOU_THRESHOLD,
            verbose=False
        )
        
        if not results or results[0].boxes is None:
            return image
        
        # 获取检测结果
        boxes = results[0].boxes.xyxy.cpu().numpy()
        confidences = results[0].boxes.conf.cpu().numpy()
        classes = results[0].boxes.cls.cpu().numpy().astype(int)
        
        # 绘制边框和标签
        for box, conf, cls in zip(boxes, confidences, classes):
            x1, y1, x2, y2 = map(int, box)
            
            # 获取类别名称
            class_name = config.CLASS_NAMES.get(cls, f"Class_{cls}")
            
            # 绘制红色边框
            cv2.rectangle(image, (x1, y1), (x2, y2), config.BBOX_COLOR, config.BBOX_THICKNESS)
            
            # 绘制标签背景
            label = f"{class_name}: {conf:.2f}"
            (text_width, text_height), _ = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, config.FONT_SCALE, config.TEXT_THICKNESS
            )
            
            cv2.rectangle(
                image, 
                (x1, y1 - text_height - 10), 
                (x1 + text_width, y1), 
                config.BBOX_COLOR, 
                -1
            )
            
            # 绘制标签文字
            cv2.putText(
                image, 
                label, 
                (x1, y1 - 5), 
                cv2.FONT_HERSHEY_SIMPLEX, 
                config.FONT_SCALE, 
                config.TEXT_COLOR, 
                config.TEXT_THICKNESS
            )
        
        return image
    
    def run(self):
        """运行检测器"""
        print("启动区域屏幕检测器...")
        print("按 'q' 退出，按 'p' 暂停/继续，按 'r' 重新设置区域")
        
        paused = False
        window_name = "植物大战僵尸检测 - 区域模式"
        
        while True:
            if not paused:
                # 截取屏幕
                screen = self.capture_screen()
                
                # 检测并绘制
                result_image = self.detect_and_draw(screen)
                
                # 添加区域信息到图像上
                if self.capture_region:
                    info_text = f"区域: {self.capture_region['width']}x{self.capture_region['height']} at ({self.capture_region['left']},{self.capture_region['top']})"
                else:
                    info_text = "区域: 全屏"
                
                cv2.putText(
                    result_image, 
                    info_text, 
                    (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 
                    0.7, 
                    (0, 255, 0), 
                    2
                )
                
                # 显示结果
                cv2.imshow(window_name, result_image)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('p'):
                paused = not paused
                status = "暂停" if paused else "运行"
                print(f"检测状态: {status}")
            elif key == ord('r'):
                print("\n重新设置截图区域...")
                cv2.destroyAllWindows()
                self.setup_capture_region()
                paused = False
            
            # 控制帧率
            time.sleep(1.0 / config.FPS)
        
        cv2.destroyAllWindows()

def main():
    """主函数"""
    try:
        detector = RegionScreenDetector()
        detector.run()
    except Exception as e:
        print(f"程序运行失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
