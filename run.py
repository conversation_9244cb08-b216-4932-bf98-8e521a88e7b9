#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
植物大战僵尸 YOLOv8 检测器启动脚本
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import ultralytics
        import cv2
        import numpy
        import mss
        import PIL
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("✗ 依赖安装失败")
        return False

def check_model():
    """检查模型文件"""
    model_files = ['best.pt', 'yolov8n.pt', 'yolov8s.pt', 'yolov8m.pt']
    
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"✓ 找到模型文件: {model_file}")
            return model_file
    
    print("✗ 未找到模型文件")
    print("请确保以下文件之一存在:")
    for model_file in model_files:
        print(f"  - {model_file}")
    
    return None

def download_pretrained_model():
    """下载预训练模型"""
    print("正在下载预训练模型...")
    try:
        from ultralytics import YOLO
        model = YOLO('yolov8n.pt')  # 这会自动下载模型
        print("✓ 预训练模型下载完成")
        return True
    except Exception as e:
        print(f"✗ 模型下载失败: {e}")
        return False

def main():
    """主函数"""
    print("植物大战僵尸 YOLOv8 检测器")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        print("\n是否安装依赖? (y/n): ", end="")
        if input().lower() == 'y':
            if not install_dependencies():
                return
        else:
            print("请手动安装依赖: pip install -r requirements.txt")
            return
    
    # 检查模型
    model_path = check_model()
    if not model_path:
        print("\n是否下载预训练模型用于测试? (y/n): ", end="")
        if input().lower() == 'y':
            if download_pretrained_model():
                model_path = 'yolov8n.pt'
            else:
                print("请手动准备模型文件")
                return
        else:
            print("请手动准备模型文件")
            return
    
    # 更新配置文件中的模型路径
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        content = content.replace('MODEL_PATH = "best.pt"', f'MODEL_PATH = "{model_path}"')
        
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ 配置文件已更新，使用模型: {model_path}")
    except Exception as e:
        print(f"✗ 配置文件更新失败: {e}")
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 透明覆盖模式 (推荐)")
    print("2. 简单窗口模式")
    print("3. 模型训练工具")
    print("4. 退出")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == '1':
        print("\n启动透明覆盖模式...")
        try:
            from screen_detector import main as run_detector
            run_detector()
        except Exception as e:
            print(f"启动失败: {e}")
    
    elif choice == '2':
        print("\n启动简单窗口模式...")
        try:
            from simple_detector import main as run_simple
            run_simple()
        except Exception as e:
            print(f"启动失败: {e}")
    
    elif choice == '3':
        print("\n启动模型训练工具...")
        try:
            from train_model import main as run_train
            run_train()
        except Exception as e:
            print(f"启动失败: {e}")
    
    elif choice == '4':
        print("退出程序")
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
